import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/navigation/navbar";
import { MapPin } from "lucide-react";
import Footer from "@/components/footer";
import ImageGallery from "@/modules/home-page/components/sections/image-gallery";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "TGN - The Good-Life Nation",
  description: "The Good-Life Nation - Extraordinary people led by the spirit",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <div className="scrollbar-hide flex items-center gap-2 py-2 px-8 text-xs">
          <MapPin size={16} />
          <span>FIND A ROYAL CHAPTER</span>
        </div>
        <Navbar />
        <main>{children}</main>
        <ImageGallery />
        <Footer />
      </body>
    </html>
  );
}
