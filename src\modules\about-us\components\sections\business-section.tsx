"use client";

import Image from "next/image";
import { Title } from "@/components/layout/title";
import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";

export default function BusinessSection() {
  return (
    <section className="py-20 bg-[#FFFFFF]">
      <Card className="relative mx-auto mt-12 max-w-[1370px] h-[660px] bg-blue-50 rounded-[65px] overflow-hidden p-0 flex flex-row-reverse">
        {/* Image Section */}
        <div className="relative w-1/2 h-full pt-7p">
          <img src="/images/about-us/building.png" alt="Welcome to TGN" className="object-cover h-full" />
        </div>

        {/* Text Section */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, x: [0, 80] }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="flex w-1/2 items-center px-12"
        >
          <div className="max-w-[600px] text-left text-[#1F254C] -translate-y-6 transform space-y-4">
            <h3 className="text-6xl font-extrabold">Business</h3>
            <p className="text-3xl font-semibold">The wisdom of God in commerce</p>
            <p className="text-base font-bold">
              Our call in business represents applying divine wisdom in economic and commercial systems. It is where our Kingdom principles meet enterprise and
              innovation.
            </p>
            <ul className="list-disc list-inside space-y-1 text-base font-bold">
              <li>RPN INNOVATIONS</li>
              <li>ASA ROCKS LTD (Subsidiaries)</li>
              <li>RNG</li>
              <li>RA</li>
              <li>The Wings of the Spirit, AA</li>
              <li>ARI</li>
              <li>ARFS</li>
              <li>RDG</li>
            </ul>
          </div>
        </motion.div>
      </Card>
    </section>
  );
}
