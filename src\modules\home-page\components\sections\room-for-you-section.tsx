"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { Title } from "@/components/layout/title";
import { Button } from "@/components/ui/button";

export default function RoomForYouSection() {
  return (
    <section className="py-20 text-white relative h-[792px] flex items-center justify-center">
      <Image
        src="/images/Room_4_u.png"
        alt="Congregation"
        fill
        className="object-cover"
      />
      <div className="container mx-auto px-4 relative">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 1.5 }}
          viewport={{ once: true }}
          className="text-center max-w-3xl mx-auto w-[900px]"
        >
          <Title className="mb-6">We&apos;ve Got Enough Room For You!</Title>
          <p className="mb-2 font-medium text-2xl">
            You weren&apos;t designed to carry life alone.
          </p>
          <p className="mb-6 font-medium text-2xl">
            In a world that glorifies independence, we choose a better way—total
            dependence on the God who made us. At TGN, we are trained to live
            from God.
          </p>
          <p className="mb-8 font-medium text-2xl">
            Ready to step into a life powered by His presence?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-tertiary text-black text-[20px] p-6 w-[320px] h-[54px] hover:bg-white/10 hover:text-primary">
              FIND A ROYAL CHAPTER
            </Button>
            <Button
              className="p-6 w-[320px] h-[54px] bg-transparent border border-white text-white hover:bg-white/10"
              variant="outline"
            >
              FILL A CONNECT CARD
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
