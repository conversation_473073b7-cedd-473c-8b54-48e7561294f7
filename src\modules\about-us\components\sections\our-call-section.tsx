"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { Title } from "@/components/layout/title";
import { Card } from "@/components/ui/card";

export default function OurCallSection() {
  return (
    <section className="py-20 bg-[#F1F8FF]">
      <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }} className="w-full text-center">
        <Title className="text-6xl font-extrabold text-[#1F254C]">Our Call</Title>
      </motion.div>

      <Card className="relative mx-auto mt-12 max-w-[1370px] h-[660px] rounded-[65px] overflow-hidden p-0">
        <div className="absolute inset-0 z-0">
          <Image src="/images/Lifted_Hands.png" alt="Welcome to TGN" fill className="object-cover" priority />
        </div>

        {/* Foreground text */}
        <motion.div
          className="relative z-10 flex h-full w-full items-center justify-end px-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, x: [100, 0] }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="max-w-[600px] text-left text-[#1F254C] -translate-y-6 transform space-y-4">
            <h3 className="text-6xl font-extrabold">Ministry</h3>
            <p className="text-3xl font-semibold">Perfecting the Saints</p>
            <p className="text-base font-bold">
              The Ministry (the Refiner’s home) is where we set people right and align them for the vision (The Kingdom of God)
            </p>
            <p className="text-base font-bold">It includes:</p>
            <ul className="list-disc list-inside space-y-1 text-base font-bold">
              <li>The Good-Life Family Ministries Int’l Inc.</li>
              <li>The Good-Life Church (Royal Chapters) TGC (RCs)</li>
              <li>Life Center (LC)</li>
              <li>College of the Kingdom (CTK)</li>
            </ul>
          </div>
        </motion.div>
      </Card>
    </section>
  );
}
