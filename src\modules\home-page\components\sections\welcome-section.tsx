"use client";

import { Title } from "@/components/layout/title";
import { motion } from "framer-motion";
import Image from "next/image";

export default function WelcomeSection() {
  return (
    <section className="relative h-[658px] py-20 text-white flex items-center justify-center">
      <Image
        src="/images/Welcome_to_TGN.png"
        alt="Welcome to TGN"
        fill
        className="object-cover"
        priority
      />
      {/* Add semi-transparent overlay */}
      <div className="absolute inset-0 bg-black/60"></div>
      <div className="container mx-auto px-4 text-center z-10 relative">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
          className=""
        >
          <Title className="text-tertiary drop-shadow-lg">Welcome To</Title>
        </motion.div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.4 }}
          className=""
        >
          <Title className="text-secondary drop-shadow-lg">
            The Good-Life Nation
          </Title>
        </motion.div>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.6 }}
          className="max-w-2xl mx-auto mb-4 drop-shadow-lg text-[20px]"
        >
          Here, giving is our life, power is our showmanship, the kingdom of God
          is our language, and its culture is our lifestyle.
        </motion.p>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.8 }}
          className="max-w-2xl mx-auto mb-8 drop-shadow-lg text-[20px]"
        >
          Yearning to be influenced and imparted with more content of God&apos;s
          personality?
          <br />
          <span className="font-bold">Welcome to your rest!</span>
        </motion.p>
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1 }}
          className="bg-[#0057FF] hover:bg-[#0046cc] text-white font-bold py-3 px-8 rounded-md drop-shadow-lg"
        >
          JOIN US LIVE
        </motion.button>
      </div>
    </section>
  );
}
