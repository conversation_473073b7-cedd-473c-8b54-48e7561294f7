import Image from "next/image";
import Link from "next/link";
import { Facebook, Instagram, Twitter, Youtube } from "lucide-react";

export default function Footer() {
  const socialIconSize = { mobile: 24, desktop: 20 };
  return (
    <footer className="bg-[#1A214D] text-white py-12 sm:py-16 md:py-20 relative">
      <div className="mx-4 sm:m-6 md:m-8">
        {/* Background image with overlay */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/background_image.jpg"
            alt="Footer background"
            fill
            className="object-cover opacity-10"
          />
        </div>

        <div className="container mx-auto px-2 sm:px-4 relative z-10 max-w-[1200px]">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 sm:gap-6">
            {/* Logo column */}
            <div className="flex justify-center md:justify-start">
              <div className="bg-white rounded-full p-3 sm:p-4 w-[120px] h-[120px] sm:w-[140px] sm:h-[140px] flex items-center justify-center">
                <Image
                  src="/logo.svg"
                  alt="The Good-Life Nation Logo"
                  width={90}
                  height={90}
                  className="object-contain sm:w-[100px] sm:h-[100px]"
                />
              </div>
            </div>

            {/* Quick links column */}
            <div>
              <h4 className="text-center sm:text-left text-lg font-medium mb-4 sm:mb-5">
                Quick links
              </h4>
              <ul className="space-y-3 sm:space-y-3">
                <li>
                  <Link
                    href="#"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    About
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    Royal Chapters
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    Sermons
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    Ministries
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    Give
                  </Link>
                </li>
              </ul>
            </div>

            {/* About us column */}
            <div>
              <h4 className="text-center sm:text-left text-lg font-medium mb-4 sm:mb-5">
                About us
              </h4>
              <ul className="space-y-3 sm:space-y-3">
                <li>
                  <Link
                    href="#"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    About RPN
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    Ministry Hierarchy
                  </Link>
                </li>
                <li>
                  <Link
                    href="/board-of-directors"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    Board of Directors
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="hover:text-[#52AEF4] text-sm block text-center sm:text-left py-1 sm:py-0"
                  >
                    Leadership Hierarchy
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact and social column */}
            <div>
              <h4 className="text-center sm:text-left text-lg font-medium mb-4 sm:mb-5">
                Contact Us
              </h4>
              <p className="text-center sm:text-left mb-2 text-sm">
                1234 Ubowo, Benin City, Edo state
              </p>
              <p className="text-center sm:text-left mb-6 sm:mb-8 text-sm">
                23480000000
              </p>

              <h4 className="text-center sm:text-left text-lg font-medium mb-4 sm:mb-5">
                Social
              </h4>
              <div className="flex justify-center sm:justify-start gap-8 sm:gap-6">
                <Link
                  href="#"
                  className="hover:text-[#52AEF4] touch-target"
                  aria-label="Facebook"
                >
                  <Facebook
                    size={socialIconSize.mobile}
                    className="sm:w-5 sm:h-5"
                  />
                </Link>
                <Link
                  href="#"
                  className="hover:text-[#52AEF4] touch-target"
                  aria-label="Instagram"
                >
                  <Instagram
                    size={socialIconSize.mobile}
                    className="sm:w-5 sm:h-5"
                  />
                </Link>
                <Link
                  href="#"
                  className="hover:text-[#52AEF4] touch-target"
                  aria-label="Twitter"
                >
                  <Twitter
                    size={socialIconSize.mobile}
                    className="sm:w-5 sm:h-5"
                  />
                </Link>
                <Link
                  href="#"
                  className="hover:text-[#52AEF4] touch-target"
                  aria-label="Youtube"
                >
                  <Youtube
                    size={socialIconSize.mobile}
                    className="sm:w-5 sm:h-5"
                  />
                </Link>
              </div>
            </div>
          </div>

          {/* Copyright section */}
          <div className="mt-12 sm:mt-16 md:mt-20 pt-4 sm:pt-6 text-center border-t border-white/10">
            <p className="text-xs sm:text-sm">
              &copy; 2025 - All Rights Reserved
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
