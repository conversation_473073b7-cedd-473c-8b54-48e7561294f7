# Modules

This directory contains major feature modules of the application. Each module should be a self-contained feature that includes its own components, types, and utilities.

## Guidelines

1. Each module should have its own directory
2. Keep related components and logic together within the module
3. Use index.ts files to export public interfaces
4. Include unit tests alongside the components

## Structure

```
modules/
├── moduleA/
│   ├── components/     # Module-specific components
│   ├── types/         # TypeScript types and interfaces
│   ├── utils/         # Module-specific utilities
│   ├── hooks/         # Module-specific hooks
│   ├── index.ts       # Public exports
│   └── README.md      # Module documentation
└── README.md          # This documentation
```

## Examples

A module could be a complete feature like:

- Authentication
- User Profile
- Dashboard
- Settings
