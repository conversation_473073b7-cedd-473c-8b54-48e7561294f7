"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { Title } from "@/components/layout/title";

export default function AboutUsHeroSection() {
  return (
    <section className="relative h-[658px] text-white overflow-hidden">
      <Image src="/images/about-us/about-us-landing.png" alt="Praise service" fill className="object-cover" priority />
      <div className="absolute inset-0"></div>
      <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-4">
        <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }} className="w-[700px]">
          <Title>About</Title>
          <Title>The Good-Life Nation</Title>
        </motion.div>
      </div>
    </section>
  );
}
