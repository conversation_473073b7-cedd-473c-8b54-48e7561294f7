"use client";

import { useEffect, useState } from "react";
// import Header from "@/modules/home-page/components/header"
import HeroSection from "@/modules/home-page/components/sections/hero-section";
import WelcomeSection from "@/modules/home-page/components/sections/welcome-section";
import RecentSermonSection from "@/modules/home-page/components/sections/recent-sermon-section";
import UpcomingProgrammesSection from "@/modules/home-page/components/sections/upcoming-programmes-section";
import IntercedeSection from "@/modules/home-page/components/sections/intercede-section";
import GiveSection from "@/modules/home-page/components/sections/give-section";
import KindnessSection from "@/modules/home-page/components/sections/kindness-section";
import RoomForYouSection from "@/modules/home-page/components/sections/room-for-you-section";
import Footer from "@/components/footer";

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <main className="min-h-screen bg-[#F0F7FF]">
      {/* <Header /> */}
      <HeroSection />
      <WelcomeSection />
      <RecentSermonSection />
      <UpcomingProgrammesSection />
      <section className="bg-gradient-to-b from-white to-[#C3D6FF] text-white">
        <IntercedeSection />
        <GiveSection />
        <KindnessSection />
      </section>
      <RoomForYouSection />
      <Footer />
    </main>
  );
}
