# TGN Frontend

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Project Structure

```
src/
├── app/             # App router pages and layouts
├── components/      # Shared components
│   ├── ui/         # UI components (buttons, inputs, etc.)
│   └── navigation/ # Navigation components
├── hooks/          # Custom React hooks
├── lib/            # Utilities and helpers
└── modules/        # Feature modules
```

### Directory Overview

- `components/`: Reusable components common to the entire project
  - `ui/`: Common UI elements like buttons, inputs, cards
  - `navigation/`: Navigation-related components like navbar
- `hooks/`: Custom React hooks for shared functionality
- `modules/`: Major feature modules with their own components and logic

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Development Guidelines

- Follow the established directory structure for new code
- Use appropriate locations for new components:
  - Common/reusable components go in `src/components`
  - Feature-specific components go in their respective module
  - Custom hooks go in `src/hooks`
- Run tests and linting before committing (automated via husky)

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
