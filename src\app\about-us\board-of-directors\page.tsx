import React from "react";
import Image from "next/image";

export default function BoardOfDirectors() {
  // President data
  const president = {
    name: "Rev. P. N<PERSON>",
    title: "The President",
    image: "/images/thumbnail1.jpg",
  };

  // Top row directors
  const topRowDirectors = [
    {
      name: "Bp. <PERSON> (BCN)",
      title: "Director of Church Ministry & Administration",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "Bp. <PERSON><PERSON><PERSON> (OS-D)",
      title: "Director of Finance",
      image: "/images/bishops/bcn.JPG",
    },
  ];

  // Middle rows and bottom row directors
  const otherDirectors = [
    {
      name: "Bp. <PERSON> (BET)",
      title: "Director of Investments",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "Bp. <PERSON> (BCP)",
      title: "Director of Programs",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "Bp. <PERSON> (BTG)",
      title: "Director of Life Centre",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "B<PERSON><PERSON> <PERSON> (B<PERSON>m-Shaw)",
      title: "Director of Concepts and Strategy",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "Bp. <PERSON><PERSON>th (BFW)",
      title: "Asst. Director of Life Centre",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "Bp. Ayo Damian (BAM)",
      title: "Asst. Director of Church  Ministry & Administration",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "Bp. Harold Anthonie (BHA)",
      title: "Director of Communications & Media",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "Bp. Goodness Barth Nathan (BGN)",
      title: "",
      image: "/images/bishops/bcn.JPG",
    },
    {
      name: "Mr. Emmanuel Goodness Pee (EMG)",
      title: "",
      image: "/images/bishops/bcn.JPG",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Background Image */}
      <div className="relative h-[660px] w-full">
        <div className="absolute inset-0 bg-black/50 z-10"></div>
        <Image
          src="/images/director_hero.JPG"
          alt="Board of Directors"
          fill
          className="object-cover object-[1%_30%]"
        />
        <div className="relative z-20 h-full flex items-center justify-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white">
            Board Of Directors
          </h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12 w-full">
        {/* Description Section */}
        <div className="bg-gray-100 p-6 rounded-lg mb-12">
          <ul className="space-y-2 text-gray-700 text-sm">
            <li>
              Leadership is a shared contribution of strategy and creativity.
            </li>
            <li>It is the leadership setting direction & giving.</li>
            <li>
              It is the leadership that sets the pace for others to follow.
            </li>
            <li>
              Leadership is the responsibility to make decisions that advance
              the mission and vision of the church.
            </li>
            <li>Leadership is the responsibility to serve others.</li>
            <li>
              Leadership is the responsibility to be accountable to God and to
              people.
            </li>
            <li>Great leaders take charge in organized chaos.</li>
          </ul>
        </div>

        {/* President Section - Center */}
        <div className="flex justify-center mb-12">
          <div className="w-64">
            <div className="relative h-[475px] w-[350px] rounded-lg overflow-hidden">
              <Image
                src={president.image}
                alt={president.name}
                fill
                className="object-cover object-center"
              />
            </div>
            <h3 className="font-semibold text-lg text-center">
              {president.name}
            </h3>
            <p className="text-gray-600 text-center">{president.title}</p>
          </div>
        </div>

        {/* Top Row - Two Directors */}
        <div className="flex flex-row justify-between mb-12">
          {topRowDirectors.map((director, index) => (
            <div key={index} className="flex flex-col items-center">
              <div className="relative h-[475px] w-[350px] rounded-lg overflow-hidden">
                <Image
                  src={director.image}
                  alt={director.name}
                  fill
                  className="object-cover object-center rounded-lg"
                />
              </div>
              <h3 className="font-semibold text-lg text-center">
                {director.name}
              </h3>
              <p className="text-gray-600 text-center">{director.title}</p>
            </div>
          ))}
        </div>

        {/* Middle Row - Three Directors */}
        <div className="flex flex-row justify-between mb-24">
          {otherDirectors.slice(0, 3).map((director, index) => (
            <div key={index} className="flex flex-col items-center">
              <div className="relative h-[475px] w-[350px] rounded-lg overflow-hidden">
                <Image
                  src={director.image}
                  alt={director.name}
                  fill
                  className="object-cover object-center"
                />
              </div>
              <h3 className="font-semibold text-lg text-center">
                {director.name}
              </h3>
              <p className="text-gray-600 text-center">{director.title}</p>
            </div>
          ))}
        </div>

        {/* Middle Row 2 - Three Directors */}
        <div className="flex flex-row justify-between mb-24">
          {otherDirectors.slice(3, 6).map((director, index) => (
            <div key={index} className="flex flex-col items-center">
              <div className="relative h-[475px] w-[350px] rounded-lg overflow-hidden">
                <Image
                  src={director.image || "/images/bishops/bcn.JPG"}
                  alt={director.name}
                  fill
                  className="object-cover object-center"
                />
              </div>
              <h3 className="font-semibold text-lg text-center">
                {director.name}
              </h3>
              <p className="text-gray-600 text-center">{director.title}</p>
            </div>
          ))}
        </div>

        {/* Bottom Row - Three Directors */}
        <div className="flex flex-row justify-between mb-24">
          {otherDirectors.slice(6).map((director, index) => (
            <div key={index} className="flex flex-col items-center">
              <div className="relative h-[475px] w-[350px] rounded-lg overflow-hidden">
                <Image
                  src={director.image}
                  alt={director.name}
                  fill
                  className="object-cover object-center"
                />
              </div>
              <h3 className="font-semibold text-lg text-center">
                {director.name}
              </h3>
              <p className="text-gray-600 text-center">{director.title}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
