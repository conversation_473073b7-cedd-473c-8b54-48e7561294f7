"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import Countdown from "@/modules/home-page/components/countdown";
import { ArrowDown } from "lucide-react";
import { Title } from "@/components/layout/title";

export default function HeroSection() {
  return (
    <section className="relative h-[658px] text-white overflow-hidden">
      <Image
        src="/images/Hero_image.png"
        alt="Worship service"
        fill
        className="object-cover"
        priority
      />
      <div className="absolute inset-0 bg-black/60"></div>
      <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-4">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="w-[700px]"
        >
          <Title>Extraordinary People Led By The Spirit</Title>
        </motion.div>
      </div>
      <div className="absolute w-[18px] h-[18px] bg-accent left-[49%] top-[500px] rounded-full flex items-center justify-center p-4">
        <span className="text-white font-bold">
          <ArrowDown size={18} />
        </span>
      </div>
      <div className="absolute bottom-0 left-0 right-0 bg-[#1f2d7a] py-3 text-center">
        <p className="text-white">
          Join us live for another God-visit in <Countdown />
        </p>
      </div>
    </section>
  );
}
