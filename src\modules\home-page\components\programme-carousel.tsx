"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { Calendar, Clock, MapPin } from "lucide-react";

const programmes = [
  {
    id: 1,
    title: "Sunday Service",
    date: "May 1, 2025",
    time: "9:00 AM",
    location: "Main Auditorium",
    description: "Join us for a powerful time of worship and the Word.",
    image: "/programme-1.jpg",
  },
  {
    id: 2,
    title: "Prayer Conference",
    date: "May 5, 2025",
    time: "6:00 PM",
    location: "Prayer Hall",
    description: "A night of intense prayer and intercession for the nations.",
    image: "/programme-2.jpg",
  },
  {
    id: 3,
    title: "Youth Empowerment",
    date: "May 10, 2025",
    time: "4:00 PM",
    location: "Youth Center",
    description: "Equipping young people with skills for kingdom advancement.",
    image: "/programme-3.jpg",
  },
];

export default function ProgrammeCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % programmes.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="max-w-4xl mx-auto relative">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
          className="rounded-xl overflow-hidden shadow-lg"
        >
          <div className="relative aspect-[16/9]">
            <Image
              src={programmes[currentIndex].image || "/placeholder.svg"}
              alt={programmes[currentIndex].title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white">
              <h3 className="text-2xl font-bold mb-2">
                {programmes[currentIndex].title}
              </h3>
              <p className="mb-4 text-white/90">
                {programmes[currentIndex].description}
              </p>

              <div className="grid grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar size={18} className="text-[#52AEF4]" />
                  <span>{programmes[currentIndex].date}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock size={18} className="text-[#52AEF4]" />
                  <span>{programmes[currentIndex].time}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin size={18} className="text-[#52AEF4]" />
                  <span>{programmes[currentIndex].location}</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      <div className="flex justify-center mt-4 gap-2">
        {programmes.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-3 h-3 rounded-full ${index === currentIndex ? "bg-[#0057FF]" : "bg-gray-300"}`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
