"use client";

import Image from "next/image";
import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";

export default function DiplomacySection() {
  return (
    <section className="py-20 bg-[#F1F8FF]">
      <Card className="relative mx-auto mt-12 max-w-[1370px] h-[660px] bg-blue-50 rounded-[65px] overflow-hidden p-0 flex flex-row">
        {/* Image Section */}
        <div className="absolute inset-0 z-0">
          <Image src="/images/about-us/flags.png" alt="Welcome to TGN" fill className="object-cover" priority />
        </div>

        {/* Text Section */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, x: [0, 80] }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="flex w-1/2 items-center px-12"
        >
          <div className="max-w-[600px] text-left text-[#1F254C] -translate-y-6 transform space-y-4">
            <h3 className="text-6xl font-extrabold">Diplomacy</h3>
            <p className="text-3xl font-semibold">Extending Kingdom Influence Across Nations</p>
            <br />
            <p className="text-base font-bold">
              Diplomacy channels our Kingdom values into governance, justice and international relations. This is where our spiritual authority intersects with
              our civic responsibilities.
            </p>
            <br />
            <ul className="list-disc list-inside space-y-1 text-base font-bold">
              <li>RPN INTERNATIONAL</li>
              <li>OMBUDSMANSHIP</li>
              <li>THE SPIRIT OF LIBERTY (SON)</li>
            </ul>
          </div>
        </motion.div>
      </Card>
    </section>
  );
}
