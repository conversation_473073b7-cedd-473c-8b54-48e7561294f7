"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  bishopsData,
  pillarsData,
} from "@/modules/tgn-hierarchy/hierarchy-data";

export default function TGNHierarchyPage() {
  const [selectedBishop, setSelectedBishop] = useState(bishopsData[0]);
  const [selectedPillar, setSelectedPillar] = useState(pillarsData[0]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-[#C3D6FF]">
      {/* Hero Section */}
      <div className="relative h-[660px] w-full">
        <div className="absolute inset-0 bg-black/60 z-10"></div>
        <Image
          src="/images/director_hero.JPG"
          alt="TGN Hierarchy"
          fill
          className="object-cover object-[1%_30%]"
        />
        <div className="relative z-20 h-full flex items-center justify-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white">
            TGN Hierarchy
          </h1>
        </div>
      </div>

      {/* The Bishops Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-12">The Bishops</h2>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left side - List of bishops */}
            <div className="lg:w-1/3 space-y-6">
              {bishopsData.map((bishop) => (
                <div
                  className={` pl-4 ${
                    selectedBishop.id === bishop.id
                      ? "border-l-4 border-[#0057FF]"
                      : ""
                  }`}
                  key={bishop.id}
                >
                  <div
                    className={`cursor-pointer transition-all ${
                      selectedBishop.id === bishop.id
                        ? "opacity-100"
                        : "opacity-70 hover:opacity-100"
                    }`}
                    onClick={() => {
                      setSelectedBishop(bishop);
                    }}
                  >
                    <h3 className="font-bold">{bishop.name}</h3>
                    <p className="text-sm text-gray-600">{bishop.title}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="lg:w-2/3">
              <div className="md:w-1/3 mb-4">
                {selectedBishop.image && (
                  <div className="relative h-80 w-full rounded-lg overflow-hidden">
                    <Image
                      src={selectedBishop.image}
                      alt={selectedBishop.name}
                      fill
                      className="object-cover object-[1%_30%]"
                    />
                  </div>
                )}
                <div className="mt-4">
                  <h3 className="font-bold text-lg">{selectedBishop.name}</h3>
                  <p className="text-gray-600">{selectedBishop.title}</p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-md bg-gradient-to-b from-white to-[#C3D6FF]">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="w-full">
                    {selectedBishop.testimony && (
                      <div className="space-y-4">
                        {selectedBishop.testimony.map((paragraph, index) => (
                          <p key={index} className="text-gray-700">
                            {paragraph}
                          </p>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* The 7 Pillars Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-12">
            The 7 Pillars
          </h2>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left side - List of bishops */}
            <div className="lg:w-1/3 space-y-6">
              {pillarsData.map((pillar) => (
                <div
                  className={` pl-4 ${
                    selectedPillar.id === pillar.id
                      ? "border-l-4 border-[#0057FF]"
                      : ""
                  }`}
                  key={pillar.id}
                >
                  <div
                    className={`cursor-pointer transition-all ${
                      selectedPillar.id === pillar.id
                        ? "opacity-100"
                        : "opacity-70 hover:opacity-100"
                    }`}
                    onClick={() => {
                      setSelectedPillar(pillar);
                    }}
                  >
                    <h3 className="font-bold">{pillar.name}</h3>
                    <p className="text-sm text-gray-600">{pillar.title}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="lg:w-2/3">
              <div className="md:w-1/3 mb-4">
                {selectedPillar.image && (
                  <div className="relative h-80 w-full rounded-lg overflow-hidden">
                    <Image
                      src={selectedPillar.image}
                      alt={selectedPillar.name}
                      fill
                      className="object-cover object-[1%_30%]"
                    />
                  </div>
                )}
                <div className="mt-4">
                  <h3 className="font-bold text-lg">{selectedPillar.name}</h3>
                  <p className="text-gray-600">{selectedPillar.title}</p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-md bg-gradient-to-b from-white to-[#C3D6FF]">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="w-full">
                    {selectedPillar.testimony && (
                      <div className="space-y-4">
                        {selectedPillar.testimony.map((paragraph, index) => (
                          <p key={index} className="text-gray-700">
                            {paragraph}
                          </p>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
