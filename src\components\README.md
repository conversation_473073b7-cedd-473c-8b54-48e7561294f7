# Components

This directory contains reusable components that are common to the entire project. These components serve as the building blocks of the application's UI and shared functionality.

## Directory Structure

- `ui/` - Common UI components (buttons, inputs, cards, etc.)
- `navigation/` - Navigation-related components (navbar, breadcrumbs, etc.)

## Guidelines

1. Components in this directory should be:

   - Highly reusable across different parts of the application
   - Generic and not tied to specific business logic
   - Well-documented with props interfaces
   - Properly typed with TypeScript

2. Component Categories:
   - UI Components: Basic interface elements (buttons, inputs, modals)
   - Layout Components: Structure-related (containers, grids)
   - Navigation Components: Navigation-related elements
   - Feedback Components: Loaders, alerts, notifications

## Example Components

- UI/
  - Button
  - Input
  - Card
  - Modal
- Navigation/
  - Navbar
  - Sidebar
  - Footer
