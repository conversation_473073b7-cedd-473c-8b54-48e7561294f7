{"name": "tgn-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "format": "prettier --write ."}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.9.2", "lucide-react": "^0.485.0", "next": "15.2.4", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.23.0", "eslint-config-next": "15.2.4", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "tailwindcss": "^4.0.17", "typescript": "^5"}}