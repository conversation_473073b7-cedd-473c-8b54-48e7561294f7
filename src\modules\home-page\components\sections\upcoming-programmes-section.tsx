import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { ArrowUpRight } from "lucide-react";
import { Title } from "@/components/layout/title";

const programmes = [
  {
    id: 1,
    title: "Sunday Service",
    date: "May 1, 2025",
    time: "9:00 AM",
    location: "Main Auditorium",
    description: "Join us for a powerful time of worship and the Word.",
    image: "/images/thumbnail1.jpg",
  },
  {
    id: 2,
    title: "Prayer Conference",
    date: "May 5, 2025",
    time: "6:00 PM",
    location: "Prayer Hall",
    description: "A night of intense prayer and intercession for the nations.",
    image: "/images/thumbnail2.jpg",
  },
  {
    id: 3,
    title: "Youth Empowerment",
    date: "May 10, 2025",
    time: "4:00 PM",
    location: "Youth Center",
    description: "Equipping young people with skills for kingdom advancement.",
    image: "/images/thumbnail3.jpg",
  },
];

export default function UpcomingProgrammesSection() {
  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Title>Upcoming Programmes</Title>
          <p className="text-gray-600 text-[20px] font-medium max-w-2xl mx-auto">
            Feed your spirit with wholesome and undiluted truth with life
            changing teachings from RPN
          </p>
        </motion.div>

        <div className="relative">
          <div className="flex space-x-6 overflow-x-auto pb-8 scrollbar-hide">
            {programmes.map((programme, index) => (
              <motion.div
                key={programme.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex-none bg-white rounded-lg overflow-hidden shadow-sm h-[400px] w-[1020px]"
              >
                <div className="relative h-full w-full">
                  <Image
                    src={programme.image}
                    alt={programme.title}
                    fill
                    className="object-cover"
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <div className="text-center mt-10">
          <Link
            href="#"
            className="text-primary flex items-center gap-1 hover:underline inline-flex justify-center font-bold"
          >
            SEE ALL PROGRAMMES
            <ArrowUpRight size={20} />
          </Link>
        </div>
      </div>
    </section>
  );
}
