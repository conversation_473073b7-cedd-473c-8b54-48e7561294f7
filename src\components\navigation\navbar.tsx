"use client";

import Link from "next/link";
import { useState, useRef, useEffect } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react";

const aboutDropdownItems = [
  { name: "About TGN", path: "/about-us" },
  { name: "Leadership", path: "/about-us/leadership" },
  { name: "Contact Us", path: "/about-us/contact-us" },
  { name: "Ministry Hierarchy", path: "/about-us/ministry-hierarchy" },
  { name: "About RPN", path: "/about-us/about-rpn" },
  { name: "Board Of Directors", path: "/about-us/board-of-directors" },
  { name: "TGN Hierarchy", path: "/about-us/tgn-hierarchy" },
];

const navItems = [
  { name: "ROYAL CHAPTERS", path: "/royal-chapters" },
  { name: "MINISTRIES", path: "/ministries" },
  { name: "SERMON", path: "/sermon" },
  { name: "GIVE", path: "/give" },
];

interface NavbarProps {
  fixed?: boolean;
}

export default function Navbar({ fixed = false }: NavbarProps) {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [aboutDropdownOpen, setAboutDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setAboutDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Check if current path is in about dropdown
  const isAboutActive = aboutDropdownItems.some(
    (item) => pathname === item.path,
  );

  return (
    <header
      className={cn(
        "bg-white shadow-sm w-full",
        fixed && "fixed top-0 left-0 right-0 z-50",
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0 flex items-center">
              <Image
                src="/logo.svg"
                alt="TGN Logo"
                width={50}
                height={50}
                className="h-10 w-auto"
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:ml-6 md:flex md:space-x-8 items-center">
            {/* About Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <div
                className={cn(
                  "inline-flex items-center px-1 pt-1 text-sm font-medium border-b-2",
                  isAboutActive
                    ? "text-secondary border-secondary"
                    : "text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300",
                )}
                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}
              >
                ABOUT US <ChevronDown className="ml-1 h-4 w-4" />
              </div>

              {aboutDropdownOpen && (
                <div className="absolute left-0 mt-2 w-92 bg-white rounded-md shadow-lg z-50 py-3 border border-gray-200">
                  <div className="flex">
                    {/* Left Column */}
                    <div className="w-1/2 pr-2">
                      {aboutDropdownItems.slice(0, 3).map((item) => (
                        <Link
                          key={item.name}
                          href={item.path}
                          className={cn(
                            "block px-4 py-2 text-sm hover:bg-gray-100",
                            pathname === item.path
                              ? "text-secondary font-medium"
                              : "text-gray-700",
                          )}
                          onClick={() => setAboutDropdownOpen(false)}
                        >
                          {item.name}
                        </Link>
                      ))}
                    </div>

                    {/* Vertical Divider */}
                    <div className="border-r border-gray-200 h-auto"></div>

                    {/* Right Column */}
                    <div className="w-1/2 pl-2">
                      {aboutDropdownItems.slice(3).map((item) => (
                        <Link
                          key={item.name}
                          href={item.path}
                          className={cn(
                            "block px-4 py-2 text-sm hover:bg-gray-100",
                            pathname === item.path
                              ? "text-secondary font-medium"
                              : "text-gray-700",
                          )}
                          onClick={() => setAboutDropdownOpen(false)}
                        >
                          {item.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Regular Nav Items */}
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.path}
                className={cn(
                  "inline-flex items-center px-1 pt-1 text-sm font-medium border-b-2",
                  pathname === item.path
                    ? "text-secondary border-secondary"
                    : "text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300",
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              type="button"
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
              aria-expanded="false"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <svg
                  className="block h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              ) : (
                <svg
                  className="block h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden">
          <div className="pt-2 pb-3 space-y-1">
            {/* Search - Mobile */}
            <div className="mx-4 flex items-center border rounded-full px-4 py-2 bg-tertiary-light">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <input
                type="text"
                placeholder="FIND A ROYAL CHAPTER"
                className="ml-2 outline-none bg-transparent w-full text-sm"
              />
            </div>

            {/* About Dropdown for Mobile */}
            <div>
              <button
                className={cn(
                  "flex w-full items-center justify-between pl-3 pr-4 py-2 text-base font-medium border-l-4",
                  isAboutActive
                    ? "text-secondary border-secondary"
                    : "border-l-4 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                )}
                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}
              >
                <span>ABOUT US</span>
                <ChevronDown className="h-4 w-4" />
              </button>

              {aboutDropdownOpen && (
                <div className="pl-6 py-2 space-y-1">
                  <div className="flex">
                    {/* Left Column */}
                    <div className="w-1/2 pr-2">
                      {aboutDropdownItems.slice(0, 3).map((item) => (
                        <Link
                          key={item.name}
                          href={item.path}
                          className={cn(
                            "block pl-3 pr-4 py-2 text-sm border-l-4",
                            pathname === item.path
                              ? "text-secondary border-secondary"
                              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                          )}
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                      ))}
                    </div>

                    {/* Vertical Divider */}
                    <div className="border-r border-gray-200 h-auto"></div>

                    {/* Right Column */}
                    <div className="w-1/2 pl-2">
                      {aboutDropdownItems.slice(3).map((item) => (
                        <Link
                          key={item.name}
                          href={item.path}
                          className={cn(
                            "block pl-3 pr-4 py-2 text-sm border-l-4",
                            pathname === item.path
                              ? "text-secondary border-secondary"
                              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                          )}
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Mobile Navigation Items */}
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.path}
                className={cn(
                  "block pl-3 pr-4 py-2 text-base font-medium border-l-4",
                  pathname === item.path
                    ? "text-secondary border-secondary"
                    : "border-l-4 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                )}
                onClick={() => setMobileMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </header>
  );
}
