# Hooks

This directory contains custom React hooks that can be reused across the application.

## Guidelines

1. Each hook should be in its own file
2. Hooks should be well-documented with clear purpose and usage examples
3. Export all hooks through the index.ts file
4. Follow the naming convention: use`use` prefix (e.g., useWindowSize, useDebounce)

## Structure

```
hooks/
├── index.ts          # Exports all hooks
├── useExample.ts     # Individual hook files
└── README.md        # This documentation
```
