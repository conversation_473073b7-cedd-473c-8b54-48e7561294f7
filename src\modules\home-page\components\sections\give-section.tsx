"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { Title } from "@/components/layout/title";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function GiveSection() {
  return (
    <section className="py-20">
      <Card className="relative mx-auto px-16 max-w-[1370px] w-full h-[660px] flex flex-row items-center justify-between bg-[#E6EEFF] rounded-[65px]">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, y: [50, 0] }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="w-[580px]"
        >
          <Title>We Give</Title>
          <p className="text-gray-700 mb-6 text-2xl">
            We see giving as a privilege, an opportunity to be aligned with the
            eternal laws and principles for prosperity
          </p>
          <Button className="bg-secondary text-tertiary mt-8 p-6">
            Give Now
          </Button>
        </motion.div>
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, y: [100, 0] }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="w-[580px]"
        >
          <Image
            src="/images/Money_Pot.png"
            alt="Plant growing in coins"
            fill
            className="object-cover"
          />
        </motion.div>
      </Card>
    </section>
  );
}
