"use client";

import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

// Define hierarchy levels
const hierarchyLevels = [
  "The Office of the President (OTP)",
  "Top Executive Council (TEC)",
  "Central Executive Council (CEC)",
  "General Executive Council (GEC)",
  "Pastoral Leadership Office (PLO)",
  "Pastoral Care Arm/ Pastoral Care Leadership (PCA/PCL)",
  "The Expansion Nation (TEN) Leadership",
  "Heads of Departments (HODs)",
  "Co-ordinators of Units (COU)",
  "Members",
];

export default function MinistryHierarchyPage() {
  return (
    <>
      <section className="relative h-[300px] sm:h-[400px] md:h-[500px] lg:h-[660px] bg-gradient-to-b from-[#001780] to-[#000322] text-white overflow-hidden">
        <Image
          src="/images/ministry-hierarchy.jpg"
          alt="Ministry Hierarchy"
          fill
          className="object-cover opacity-40 object-top"
          priority
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold px-4 text-center">
            Ministry Hierarchy
          </h1>
        </div>
      </section>

      {/* President Section */}
      <section className="relative h-auto min-h-[500px] sm:min-h-[600px] md:min-h-[700px] lg:h-[800px] py-12 lg:py-0">
        <Image
          src="/images/WebLightGradient.png"
          alt="Ministry Hierarchy"
          fill
          className="object-cover hidden lg:block"
          priority
        />
        <div className="absolute relative inset-0 flex flex-col lg:flex-row items-center">
          {/* Image container for mobile */}
          <div className="w-full lg:w-1/2 flex justify-center items-center mb-8 lg:mb-0 px-4 lg:px-0 lg:hidden">
            <div className="relative w-full max-w-sm aspect-square">
              <Image
                src="/images/president.jpg"
                alt="Rev. Paul Nathan Utomi"
                fill
                className="object-cover rounded-xl"
              />
            </div>
          </div>

          <div className="hidden lg:block lg:w-1/2"></div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="w-full lg:w-1/2 space-y-8 px-6 lg:px-4 lg:mt-12"
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-bold mb-6 text-center lg:text-left">
              Our President
            </h2>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl">
              Rev. Paul Nathan Utomi is the president of The Good-Life Nation.
              The LORD has called RPN His number one intercessor, one who is
              committed wholeheartedly to prayer and also to raising a people
              who are instant in prayer, who pray without ceasing and who
              prevail in prayer.
            </p>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl">
              RPN is a homologue of God, constantly communicating the word of
              God with accuracy and precision, hereby raising a people addicted
              to truth.
            </p>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl">
              RPN is a Rabbi, with the acute perception of God called to raise
              6.2 billion extraordinary people, led by the Spirit, prepared for
              the LORD.
            </p>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl">
              RPN is the herald of the 7th Generation, called to lead a people
              raised and sealed for the need of God.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Hierarchy Structure Section */}
      <section className="relative">
        <div className="relative w-full h-[300px] sm:h-[400px] md:h-[500px] lg:h-[700px] bg-[#1A214D]">
          <Image
            src="/images/background_image.jpg"
            alt="Ministry Hierarchy"
            fill
            className="object-cover opacity-40 object-top"
            priority
          />
        </div>
        <div className="w-full h-[300px] sm:h-[400px] md:h-[500px] lg:h-[700px] bg-[#F0F7FF]"></div>
        <div className="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[90%] sm:w-[85%] md:w-[80%] lg:w-[975px] max-w-[975px]">
          <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6 md:p-8">
            <h2 className="text-2xl sm:text-3xl md:text-[35px] lg:text-[40px] font-bold text-center mb-4 sm:mb-6 md:mb-8">
              Ministry Hierarchy
            </h2>

            <div className="space-y-2 sm:space-y-3 md:space-y-4">
              {hierarchyLevels.map((level, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="text-center text-sm sm:text-base md:text-xl lg:text-2xl font-medium py-1 sm:py-2 border-b border-gray-100 last:border-b-0"
                >
                  {level}
                </motion.div>
              ))}
            </div>
          </div>
          <div className="mt-4 sm:mt-6 md:mt-8 text-center">
            <Link
              href="/hierarchy"
              className="inline-flex items-center justify-center bg-[#001780] hover:bg-[#000d4d] text-white font-medium py-2 px-4 sm:px-6 rounded-md transition-colors"
            >
              Learn more
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
