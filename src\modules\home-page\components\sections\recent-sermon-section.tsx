"use client";

import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import { Play, ArrowUpRight } from "lucide-react";
import { useState } from "react";
import { Title } from "@/components/layout/title";

const sermons = [
  {
    id: 1,
    title: "The Audacity of Sacrifice",
    speaker: "RPN",
    date: "April 23, 2025",
    thumbnail: "/images/thumbnail1.jpg",
  },
  {
    id: 2,
    title: "Walking in the Spirit",
    speaker: "RPN",
    date: "April 16, 2025",
    thumbnail: "/images/thumbnail2.jpg",
  },
  {
    id: 3,
    title: "Kingdom Principles",
    speaker: "RPN",
    date: "April 9, 2025",
    thumbnail: "/images/thumbnail3.jpg",
  },
  {
    id: 4,
    title: "The Higher Life",
    speaker: "RPN",
    date: "April 2, 2025",
    thumbnail: "/images/thumbnail1.jpg",
  },
  {
    id: 5,
    title: "The Spirit of Excellence",
    speaker: "RPN",
    date: "March 26, 2025",
    thumbnail: "/images/thumbnail2.jpg",
  },
  {
    id: 6,
    title: "The Concept of Victory",
    speaker: "RPN",
    date: "March 19, 2025",
    thumbnail: "/images/thumbnail3.jpg",
  },
];

export default function RecentSermonSection() {
  const [currentPage] = useState(0);
  const sermonsPerPage = 4;

  const currentSermons = sermons.slice(
    currentPage * sermonsPerPage,
    (currentPage + 1) * sermonsPerPage,
  );

  return (
    <section className="relative">
      <div className="py-20 h-[800px]">
        <div className="container mx-auto px-4 mt-16">
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 max-w-[700px] mx-auto"
          >
            <Title>Recent Teachings</Title>
            <p className="text-gray-600 text-[20px] font-medium max-w-2xl mx-auto">
              Feed your spirit with wholesome and undiluted truth with life
              changing teachings from RPN
            </p>
          </motion.div>
        </div>
      </div>
      <div className="absolute top-[25%] inset-x-4 left- z-10">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="max-w-5xl mx-auto rounded-2xl overflow-hidden relative"
        >
          <Image
            src="/images/thumbnail3.jpg"
            alt="Recent Sermon"
            width={1020}
            height={590}
            className="h-[600px] w-[1020px] object-cover object-[1%_30%]"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-center justify-center">
            <button className="w-16 h-16 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/40 transition-colors">
              <Play size={30} className="text-white ml-1" />
            </button>
          </div>
        </motion.div>
      </div>
      <div className="bg-[url('/images/background_image.jpg')] bg-cover bg-center relative h-[620px] flex items-center">
        <div className="absolute inset-0 bg-black/50"></div>
        <div className="relative z-10 max-w-[1020px] mx-auto text-white py-12 px-4 w-full self-end">
          <div className="flex items-center justify-between mb-10">
            <h3 className="text-[36px] font-medium">More recent teachings</h3>
            <div className="flex items-center gap-4">
              <Link
                href="#"
                className="text-primary flex font-bold items-center gap-1 hover:underline"
              >
                SEE ALL TEACHINGS
                <ArrowUpRight />
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 p-4 mx-auto">
            {currentSermons.map((sermon) => (
              <div key={sermon.id} className="group flex flex-col items-center">
                <div className="relative rounded-lg overflow-hidden w-full max-w-[235px]">
                  <Image
                    src={sermon.thumbnail || "/images/placeholder.svg"}
                    alt={sermon.title}
                    width={300}
                    height={200}
                    className="w-full aspect-video object-cover object-[1%_30%] group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <button className="w-10 h-10 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center">
                      <Play size={20} className="text-white ml-0.5" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
