"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Title } from "@/components/layout/title";
import { Card } from "@/components/ui/card";

export default function IntercedeSection() {
  return (
    <section className="py-20">
      <Card className="relative mx-auto px-4 max-w-[1370px] h-[660px] flex items-center justify-center bg-[#E6EEFF] rounded-[65px]">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, y: [-100, 0] }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className=""
        >
          <Image
            src="/images/Hands_Together.png"
            alt="Welcome to TGN"
            fill
            className="object-cover rounded-[65px]"
            priority
          />
        </motion.div>

        <div className="text-center max-w-[612px] mx-auto">
          <Title>We Intercede</Title>
          <p className="text-gray-700 text-2xl mb-6">
            Got a matter to fix? We&apos;re fixers! In moments of need, we stand
            with you in faith, offering support and hope.
          </p>
          <Button className="bg-secondary text-tertiary mt-8 p-6">
            Submit Request
          </Button>
        </div>
      </Card>
    </section>
  );
}
