"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { Title } from "@/components/layout/title";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function KindnessSection() {
  return (
    <section className="py-20">
      <Card className="relative mx-auto px-16 max-w-[1370px] w-full h-[660px] flex flex-row items-center justify-between bg-[#E6EEFF] rounded-[65px]">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, x: [-100, 0] }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="md:w-1/2 flex item-left"
        >
          <Image
            src="/images/hand_raised.png"
            alt="Hands raised"
            width={600}
            height={600}
            className="rounded-lg"
          />
        </motion.div>
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1, x: [100, 0] }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="md:w-1/2 text-center md:text-right"
        >
          <Title>We Return Kindness</Title>
          <p className="text-gray-700 mb-6 text-2xl">
            As a covenant people who constantly experience the wonders of God,
            our faith-filled truths in reality are the proof of the King of
            Israel in our midst.
          </p>
          <Button className="bg-secondary text-tertiary mt-8 p-6">
            See Truth In Reality
          </Button>
        </motion.div>
      </Card>
    </section>
  );
}
