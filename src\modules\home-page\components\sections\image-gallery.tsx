import Image from "next/image";

export default function ImageGallery() {
  return (
    <section className="w-full">
      <div className="flex">
        {[1, 2, 3, 4, 5].map((item) => (
          <div key={item} className="aspect-[4/3]">
            <Image
              src={`/images/gallery_${item}.JPG`}
              alt={`Gallery image ${item}`}
              width={200}
              height={150}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>
    </section>
  );
}
